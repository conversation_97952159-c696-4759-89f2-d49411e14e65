'use client';

import { useEffect, useState } from 'react';
import { redirect } from 'next/navigation';
import { useGlobalStore } from '@/store/global.store';
import { useCurrentUser } from '@/hooks/query/user';

interface AuthGuardProps {
  children: React.ReactNode;
}

export function AuthGuard({ children }: AuthGuardProps) {
  const { isSignedIn } = useGlobalStore();
  const { isLoading } = useCurrentUser();
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    if (isLoading) {
      setIsInitialized(true);
    }
  }, [isLoading]);

  useEffect(() => {
    if (isInitialized && !isSignedIn) {
      redirect('/');
    }
  }, [isInitialized, isSignedIn]);

  return <>{children}</>;
}
