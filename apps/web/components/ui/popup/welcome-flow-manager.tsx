'use client';

import { useEffect, useState, useCallback } from 'react';
import { useCurrentUser } from '@/hooks/query/user';
import { usePopupStore } from './popup.state';
import WelcomeFlowPopup from './welcome-flow-popup';
import { useCreateUser } from '@/hooks/query/user/use-create-user';
import { useGlobalStore } from '@/store/global.store';

const getUrlParams = () => {
  if (typeof window !== 'undefined') {
    const urlParams = new URLSearchParams(window.location.search);
    return {
      forceShowWelcome: urlParams.get('showWelcome') === 'true',
      referralCode: urlParams.get('referralCode') || undefined,
    };
  }
  return { forceShowWelcome: false, referralCode: undefined };
};

export function WelcomeFlowManager() {
  const { isLoading, error, isSignedIn, refetch } = useCurrentUser();
  const { safeSmartAccount } = useGlobalStore();
  const { openPopup, closePopup, isOpen } = usePopupStore();
  const createUser = useCreateUser();

  // 사용자 생성 중 상태 관리 (무한 요청 방지)
  const [isCreatingUser, setIsCreatingUser] = useState(false);

  const handleCreateNewUser = useCallback(
    async (referralCode?: string) => {
      if (!safeSmartAccount?.address || isCreatingUser) return;

      setIsCreatingUser(true);

      try {
        // referralCode가 있으면 포함하여 사용자 생성
        const createUserData = referralCode ? { referralCode } : {};
        await createUser.mutateAsync(createUserData);
        await refetch();

        // 사용자 생성 후 환영 플로우 표시
        openPopup(
          <WelcomeFlowPopup
            onClose={() => {
              closePopup();
            }}
          />
        );
      } catch (error) {
        console.error('Failed to create user', error);
      } finally {
        setIsCreatingUser(false);
      }
    },
    [safeSmartAccount?.address, isCreatingUser, refetch, createUser, openPopup, closePopup]
  );

  useEffect(() => {
    const { forceShowWelcome, referralCode } = getUrlParams();

    if (forceShowWelcome) {
      if (!isOpen) {
        openPopup(
          <WelcomeFlowPopup
            onClose={() => {
              closePopup();
            }}
          />
        );
      }
      return;
    }

    if (!isSignedIn || isLoading) return;
    if (isOpen) return;
    if (isCreatingUser) return; // 사용자 생성 중이면 대기

    const isNewUser = error && error.message.includes('404');
    const currentAddress = safeSmartAccount?.address;

    if (isNewUser && currentAddress) {
      handleCreateNewUser(referralCode);
    }
  }, [
    isSignedIn,
    isLoading,
    error,
    isOpen,
    isCreatingUser,
    safeSmartAccount?.address,
    handleCreateNewUser,
    closePopup,
    openPopup,
  ]);

  return null;
}
