import { useQuery } from '@tanstack/react-query';
import { referralKeys } from '../query-keys';
import { referralService } from '@/lib/api/referral/referral.service';

export const useReferralBenefits = () => {
  return useQuery({
    queryKey: referralKeys.benefits(),
    queryFn: () => referralService.getReferralBenefits(),
    select: data => data.benefits,
  });
};

export type UseReferralBenefitsData = NonNullable<
  Awaited<ReturnType<typeof useReferralBenefits>>['data']
>;
