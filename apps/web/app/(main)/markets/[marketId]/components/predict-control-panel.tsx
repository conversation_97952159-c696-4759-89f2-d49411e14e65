import PredictButton from '@/components/actions/predict-button';
import MarketCountdown from '@/components/common/market-countdown';
import PredictionMarketCardStatus from '@/components/common/prediction-market-card-status';
import SvgIcon from '@/components/icons/svg-icon';
import CommonAvatar from '@/components/ui/avatar-image';
import { DollarInput } from '@/components/ui/dollar-input';
import type { MarketProps } from '@/hooks/query/market/market.mapper';
import { useAuthGuard } from '@/hooks/use-auth-guard';
import { useMyUSDCBalance } from '@/hooks/use-usdc-balance';
import type { MarketOutcome } from '@/lib/api/market/market.transform';
import { ICON_PATH } from '@/lib/constants';
import { toAmount } from '@/lib/format';
import { getGraphVar } from '@/lib/styles';
import { getEsimatedOdds } from '@/lib/utils';
import { Tooltip, TooltipContent, TooltipTrigger } from '@repo/ui/components/tooltip';
import { cn } from '@repo/ui/lib/utils';
import BigNumber from 'bignumber.js';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

interface PredictControlPanelProps {
  marketData: MarketProps;
  selectedOutcome?: MarketOutcome | null;
}

function PredictControlPanelContent({
  marketData,
  selectedOutcome,
  setShowRefreshOverlay,
}: PredictControlPanelProps & {
  setShowRefreshOverlay: (value: boolean) => void;
}) {
  const {
    marketId,
    marketTitle,
    marketAvatarImageUrl,
    marketOutcomes,
    marketMaxOutcomeVolume,
    marketTotalVolume,
    marketNextDeadline,
    marketStatus,
  } = marketData;

  const { balance: userBalance } = useMyUSDCBalance();
  const [amount, setAmount] = useState<number>(0);
  const userBalanceNumber = userBalance ? parseFloat(userBalance) : 0;
  const { withAuthCheck } = useAuthGuard();

  // Use selectedOutcome or fall back to first outcome
  const currentOutcome = selectedOutcome ?? marketOutcomes[0];
  if (!currentOutcome) {
    return (
      <div className="p-space-30">
        <div className="text-size-sm flex flex-col items-center font-semibold">
          <span className="text-red-500">Error</span>
          <span>No outcome selected</span>
        </div>
      </div>
    );
  }

  const currentRawOutcomeVolume = marketOutcomes.find(o => o.order === currentOutcome.order)?.volume
    .raw;

  if (currentRawOutcomeVolume === undefined) {
    return (
      <div className="p-space-30">
        <div className="text-size-sm flex flex-col items-center font-semibold">
          <span className="text-red-500">Error</span>
          <span>Outcome data is incomplete</span>
        </div>
      </div>
    );
  }

  const availableToPredict = marketMaxOutcomeVolume.raw
    .minus(currentRawOutcomeVolume)
    .dividedBy(1000000)
    .toNumber();

  const handleAmountChange = (value: number) => {
    setAmount(value);
  };

  const handleAddAmount = withAuthCheck((addValue: number) => {
    const newAmount = amount + addValue;
    const maxAllowedAmount = Math.min(userBalanceNumber, availableToPredict);

    if (newAmount <= maxAllowedAmount) {
      setAmount(newAmount);
    } else {
      setAmount(maxAllowedAmount);
    }
  });

  const handleMaxAmount = withAuthCheck(() => {
    setAmount(Math.min(userBalanceNumber, availableToPredict));
  });

  const handlePredictSuccess = () => {
    setAmount(0); // Reset amount after successful prediction
  };

  const percentage = currentOutcome.volume.raw
    .dividedBy(marketMaxOutcomeVolume.raw)
    .multipliedBy(100)
    .toNumber();

  const toAddAmount = BigNumber(toAmount(amount));
  const estimatedVolume = currentOutcome.volume.raw.plus(toAddAmount);
  const estimatedOdds = getEsimatedOdds(marketTotalVolume.raw.plus(toAddAmount), estimatedVolume);
  const estmiatedWinnings = parseFloat(estimatedOdds) * amount;

  const currentOutcomePercentage = currentOutcome.volume.raw
    .dividedBy(marketTotalVolume.raw)
    .multipliedBy(100)
    .toFixed(2);

  return (
    <div className={cn('flex flex-col')}>
      <h1 className="pb-space-30 dashboard-h2">Prediction</h1>

      {/* Market Info */}
      <div className="mb-space-30 flex items-center">
        <div className="mr-space-15 flex-shrink-0">
          <CommonAvatar imageUrl={marketAvatarImageUrl} size="md3" alt={marketTitle} />
        </div>
        <div className="flex-1">
          <h2 className="text-size-sm text-dark line-clamp-1 font-semibold">{marketTitle}</h2>
          {currentOutcome.outcome && (
            <>
              <div className="gap-space-6 text-size-xs flex items-center">
                <span className="text-mid-dark max-w-[20ch] truncate font-semibold">
                  {currentOutcome.outcome}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <div
                  className="bg-no-red rounded-2 h-2"
                  style={{
                    backgroundColor: getGraphVar(currentOutcome.order),
                    width: `${currentOutcomePercentage}%`,
                  }}
                ></div>
                <div className="text-size-xs text-mid-dark font-semibold">
                  {currentOutcomePercentage}%
                </div>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Available to Predict */}
      <div className="mb-space-30">
        <div className="mb-space-20 flex items-center">
          <h3 className="text-size-sm font-semibold">Available to Predict</h3>
          &nbsp;
          <Tooltip>
            <TooltipTrigger>
              <SvgIcon name="RedAlertIcon" />
            </TooltipTrigger>
            <TooltipContent>
              <p>
                Maximum predictable volume per
                <br />
                outcome set by the channel owner
              </p>
            </TooltipContent>
          </Tooltip>
        </div>

        {/* Progress bar */}
        <div className="mb-space-6 bg-gray-1 relative h-2 rounded-full">
          <div
            className="bg-no-red absolute top-0 left-0 h-full rounded-full"
            style={{
              width: `${percentage}%`,
            }}
          ></div>
        </div>

        <div className="flex justify-between">
          <span className="text-size-xs text-no-red font-semibold">
            ${currentOutcome.volume.formatted}
          </span>
          <div className="text-right">
            <span className="text-size-xs text-gray-3">Max</span>
            <span className="ml-space-6 text-size-xs font-semibold">
              ${marketMaxOutcomeVolume.formatted}
            </span>
          </div>
        </div>
      </div>

      {/* Amount */}
      <div className="mb-space-10">
        <h3 className="text-size-sm mb-space-10 font-semibold">Amount</h3>
        <div className="bg-gray-2 px-space-10 py-space-20 flex flex-col">
          <div className="text-size-xs text-gray-3 mb-space-10 text-right">
            My Balance ${userBalance}
          </div>

          {/* Amount Input */}
          <div className="mb-space-10">
            <DollarInput
              placeholder="$1"
              className="text-dark text-size-xl px-space-10 h-(--input-height-lg) text-right font-bold"
              value={amount}
              onChange={handleAmountChange}
              maxValue={availableToPredict}
              minValue={1}
            />
          </div>

          {/* Amount Buttons */}
          <div className="gap-space-10 flex justify-end">
            <button
              className="border-line py-space-6 px-space-10 text-size-sm hover:bg-gray-1 rounded-sm border bg-white font-medium transition-colors"
              onClick={() => handleAddAmount(1)}
            >
              +$1
            </button>
            <button
              className="border-line py-space-6 px-space-10 text-size-sm hover:bg-gray-1 rounded-sm border bg-white font-medium transition-colors"
              onClick={() => handleAddAmount(20)}
            >
              +$20
            </button>
            <button
              className="border-line py-space-6 px-space-10 text-size-sm hover:bg-gray-1 rounded-sm border bg-white font-medium transition-colors"
              onClick={() => handleAddAmount(100)}
            >
              +$100
            </button>
            <button
              className="border-line py-space-6 px-space-10 text-size-sm hover:bg-gray-1 rounded-sm border bg-white font-medium transition-colors"
              onClick={handleMaxAmount}
            >
              Max
            </button>
          </div>
        </div>
      </div>

      {/* Predict Button */}
      <div className="mb-space-10">
        <PredictButton
          textSize="base"
          marketId={marketId}
          outcome={currentOutcome.outcome}
          amount={amount}
          className="w-full font-bold"
          onSuccess={handlePredictSuccess}
          estimatedOdds={estimatedOdds}
        />
      </div>

      {/* Market Status and Time Remaining */}
      <div className="mb-space-15 gap-space-8 flex items-center justify-end">
        <PredictionMarketCardStatus status={marketStatus} />
        {marketNextDeadline && (
          <MarketCountdown
            endTime={marketNextDeadline}
            onComplete={() => {
              setShowRefreshOverlay(true);
            }}
          />
        )}
      </div>

      {/* Additional Info */}
      <div className="gap-space-6 flex flex-col">
        <div className="text-size-xs flex justify-between font-semibold">
          <Tooltip>
            <TooltipTrigger>
              <span className="text-gray-3 cursor-help underline">Execution Fee</span>
            </TooltipTrigger>
            <TooltipContent className="max-w-fit">
              <p>
                For a better user experience,
                <br /> PredictGo applies a fee to cover
                <br /> all associated network charges.
              </p>
            </TooltipContent>
          </Tooltip>
          <span className="font-medium">$0.1</span>
        </div>
        <div className="text-size-xs flex justify-between font-semibold">
          <span className="text-gray-3">Estimated odds</span>
          <span className="font-medium">{estimatedOdds}</span>
        </div>
        <div className="text-size-sm flex justify-between font-bold">
          <span className="font-medium">Estimated Winnings</span>
          <span className="font-medium">${estmiatedWinnings.toFixed(2)}</span>
        </div>
        <p className="text-no-red text-size-xs">
          Current estimates apply, but final odds lock in upon betting closure.
        </p>
      </div>
    </div>
  );
}

export default function PredictControlPanel(props: PredictControlPanelProps) {
  const [showRefreshOverlay, setShowRefreshOverlay] = useState(false);
  if (showRefreshOverlay) {
    return (
      <div className="relative">
        <div className="absolute inset-0 z-10 flex items-center justify-center bg-white/80 backdrop-blur-sm">
          <div className="flex flex-col items-center justify-center gap-[10px]">
            <img src={ICON_PATH.REFRESH} className="size-[30px]" />
            <h3 className="text-center text-[12px] font-semibold">Please refresh the page</h3>
          </div>
        </div>

        <div className="pointer-events-none px-[30px] opacity-50">
          <PredictControlPanelContent setShowRefreshOverlay={setShowRefreshOverlay} {...props} />
        </div>
      </div>
    );
  }

  return (
    <div className="px-[30px]">
      <PredictControlPanelContent setShowRefreshOverlay={setShowRefreshOverlay} {...props} />
    </div>
  );
}
