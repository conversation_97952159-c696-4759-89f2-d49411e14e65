import { useReferralBenefits } from '@/hooks/query/referral';
import type { UseReferralBenefitsData } from '@/hooks/query/referral/use-referral-benefits';

function RewardReferralBenefitsSkeleton() {
  return (
    <div className="divide-line text-size-sm text-mid-dark px-space-30 divide-y font-semibold">
      {Array.from({ length: 5 }).map((_, index) => (
        <div key={index} className="py-space-10 px-space-20 grid grid-cols-4 items-center">
          <div className="h-4 w-12 animate-pulse rounded bg-gray-200"></div>
          <div className="h-4 w-16 animate-pulse rounded bg-gray-200"></div>
          <div className="h-4 w-16 animate-pulse rounded bg-gray-200"></div>
          <div className="h-4 w-20 animate-pulse rounded bg-gray-200"></div>
        </div>
      ))}
    </div>
  );
}

export function RewardReferralBenefits({ colStyles }: { colStyles: Record<string, any> }) {
  const { data: benefits, isLoading } = useReferralBenefits();

  if (isLoading || !benefits) {
    return <RewardReferralBenefitsSkeleton />;
  }

  const getCommissionReward = (commissionRewardRatio: string) => {
    const ratio = parseFloat(commissionRewardRatio);
    return `${ratio * 10}%`;
  };

  const getFeeRebate = (feeRebateRatio: string) => {
    const ratio = parseFloat(feeRebateRatio);
    return `${ratio * 10}%`;
  };

  const getAccumulatedProfit = (accumulatedProfit: string) => {
    return `$${accumulatedProfit.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`;
  };

  // Commission Reward 랑 Fee Rebate에는 곱하기 10, 누적 Profit에는 $랑 천단위 컴마표시 부탁드립니다
  const renderRow = (benefit: UseReferralBenefitsData[number], index: number) => {
    return (
      <div key={index} className="py-space-10 px-space-20 flex items-center justify-between">
        <div style={colStyles.level}>LV.{benefit.level}</div>
        <div style={colStyles.commissionRewardRatio}>
          {getCommissionReward(benefit.commissionRewardRatio)}
        </div>
        <div style={colStyles.feeRebateRatio}>{getFeeRebate(benefit.feeRebateRatio)}</div>
        <div style={colStyles.accumulatedProfit}>
          ≥ {getAccumulatedProfit(benefit.accumulatedProfit)}
        </div>
      </div>
    );
  };

  return (
    <div className="divide-line text-size-sm text-mid-dark divide-y font-semibold">
      {benefits.map(renderRow)}
    </div>
  );
}
