'use client';

import { BaseButton } from '@/components/ui/base.button';
import { RewardReferralBenefits } from './components/reward-referral-benefits';
import { RewardReferralDashboard } from './components/reward-referral-dashboard';
import RewardReferralLeaderboard from './components/reward-referral-learboard';
import { EXTERNAL_LINKS } from '@/lib/constants';
import { useAuthGuard } from '@/hooks/use-auth-guard';
import { useRouter } from 'next/navigation';

export default function ReferralPage() {
  const router = useRouter();
  const { withAuthCheck } = useAuthGuard();

  const handleClickCheckReferralCode = withAuthCheck(() => {
    router.push('/profile/referral');
  });

  const colsWidths = {
    level: {
      width: '80px',
      textAlign: 'center',
    },
    commissionRewardRatio: {
      width: '120px',
      textAlign: 'right',
    },
    feeRebateRatio: {
      width: '65px',
      textAlign: 'right',
    },
    accumulatedProfit: {
      width: '120px',
      textAlign: 'center',
    },
  };

  return (
    <div className="page">
      <div className="flex flex-col">
        <section className="gap-space-60 border-b-line flex border-b pb-[70px]">
          {/* TODO: image section */}
          <div className="w-[500px]">
            <img
              alt="Referral Guidance"
              className="w-full"
              src="/assets/images/referral_object1.png"
            />
          </div>
          <div className="gap-space-30 flex flex-1 flex-col">
            <h2 className="text-size-xl text-dark font-bold">
              Participate & Receive 1% Referral Commission on you and your friends Winnings
            </h2>
            <div className="text-size-sm text-gray-3">
              Find out more about how PredictGo Referral works!{' '}
              <a
                className="text-dark underline"
                href={EXTERNAL_LINKS.SERVICE_DOCS_REFERRAL}
                target="_blank"
                rel="noreferrer"
              >
                View Rules
              </a>
            </div>
            <h3 className="dashboard-h2">Referral Benefits</h3>
            <div className="mt-space-20 mb-space-30">
              <div className="bg-gray-2">
                <div className="bg-gray-2 text-size-xs text-gray-3 py-space-15 px-space-20 border-b-line flex justify-between border-b font-semibold">
                  <div style={{ width: colsWidths.level.width }}>Referral Level</div>
                  <div style={{ width: colsWidths.commissionRewardRatio.width }}>
                    Commission Reward
                  </div>
                  <div style={{ width: colsWidths.feeRebateRatio.width }}>Fee Rebate</div>
                  <div style={{ width: colsWidths.accumulatedProfit.width }}>
                    Accumulated Profit
                  </div>
                </div>
                <RewardReferralBenefits colStyles={colsWidths} />
              </div>
              <div className="gap-space-30 mt-space-20 flex items-center justify-end">
                <p className="text-size-sm">
                  Get up to 20% rebate when you invite friends to PredictGo!
                </p>
                <BaseButton
                  className="px-space-20"
                  size="sm"
                  variant="info"
                  onClick={handleClickCheckReferralCode}
                >
                  Check Referral Code
                </BaseButton>
              </div>
            </div>
          </div>
        </section>
        <RewardReferralDashboard />
        <section className="bg-sky flex h-[260px] flex-col items-center justify-between py-[50px]">
          <div className="flex flex-col items-center gap-[10px] text-white">
            <h2 className="text-size-2xl font-bold">
              Become an affiliate and receive more referral Benefits!
            </h2>
            <p className="text-size-base text-center font-bold">
              Are you a content creator, community group leader, or a KOL?
              <br /> Apply to join our Affiliate Program to receive special referral comission and
              platform benefits for promoting PredictGo!
            </p>
          </div>
          <a
            className="text-sky hover:bg-gray-1 flex h-[36px] w-[160px] items-center justify-center rounded-[4px] bg-white font-semibold transition-colors"
            href={'#'}
            target="_blank"
            rel="noreferrer"
          >
            Apply Now
          </a>
        </section>
        <RewardReferralLeaderboard />
      </div>
    </div>
  );
}
